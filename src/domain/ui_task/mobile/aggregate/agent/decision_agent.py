#!/usr/bin/env python3
"""
决策Agent

负责分析截图、生成思考过程和决定下一步动作（不包含坐标）
"""

import base64
import json
import time
from typing import Tuple, Any

from langchain_core.prompts import ChatPromptTemplate
from loguru import logger

from src.domain.ui_task.mobile.aggregate.agent.verification_agent import expectation_verification_agent
from src.domain.ui_task.mobile.aggregate.prompt.decision_definition_prompt import *
from src.domain.ui_task.mobile.aggregate.prompt.decision_invoke_prompt import *
from src.domain.ui_task.mobile.android.screenshot_manager import \
    screenshot_manager, convert_screenshot_to_base64
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler, AgentExceptionHandler
from src.infra.model import get_chat_model


class DecisionAgent:
    """决策Agent - 负责分析和决策"""

    def __init__(self):
        self.model = get_chat_model()
        self.last_step_name = ""  # 记录上次的步骤名称，用于检测步骤变化

    def analyze_and_decide(self, state: DeploymentState,
                           before_screenshot_path: str) -> tuple:
        """
            分析截图和测试用例上下文，生成思考过程和决定下一步动作

            Args:
                state: 当前状态
                before_screenshot_path: 截图的base64数据

            Returns:
                Tuple[parsed_fields, action_line]: 解析的JSON字段、动作命令
            """
        # 构建消息
        task_id = state["task_id"]
        try:
            image_data_base64 = convert_screenshot_to_base64(before_screenshot_path, task_id)
            messages = self._build_complete_test_case_messages(state, image_data_base64)
            start_time = time.time()
            prompt = ChatPromptTemplate.from_messages(messages=messages)
            chain = prompt | self.model
            output = chain.invoke({})
            model_response = output.content

            # config = get_or_create_settings_ins()
            # client = OpenAI(
            #     base_url=config.paths.doubao_base_url,
            #     api_key=config.paths.doubao_api_key
            # )
            # chat_completion = client.chat.completions.create(
            #     model=config.paths.doubao_model,
            #     messages=messages,
            #     temperature=0.1,
            #     # extra_body={
            #     #     "thinking": {
            #     #         #"type": "disabled",  # 不使用深度思考能力
            #     #         #"type": "enabled",  # 使用深度思考能力
            #     #         # "type": "auto", # 模型自行判断是否使用深度思考能力
            #     #     }
            #     # },
            # )
            # model_response = chat_completion.choices[0].message.content

            logger.info(f"[task_id: {task_id}] 决策模型完整响应: \n {model_response}")

            # 解析JSON响应
            parsed_fields, action_line = DecisionAgent._parse_json_response(
                model_response, state)

            # 记录当前步骤名称，用于步骤变化检测
            current_step_name = parsed_fields.get("current_step_name", "")
            step_name_changed = True if not current_step_name else current_step_name != self.last_step_name
            parsed_fields["step_name_changed"] = step_name_changed
            parsed_fields["previous_step_name"] = self.last_step_name

            # 判断步骤切换方向（向上回退 vs 向下前进）
            step_direction = self._determine_step_direction(state, current_step_name, self.last_step_name)
            parsed_fields["step_direction"] = step_direction

            # 检查是否是验证失败后的回滚操作
            is_rollback_after_verification_failure = state.get("verification_failure_reason") is not None

            # 在决策后立即进行预期校验（仅当步骤向下切换时）
            if (step_name_changed and self.last_step_name and
                state.get("verification_mode") == "step_by_step" and
                not is_rollback_after_verification_failure and
                step_direction == "forward"):  # 只有向下切换时才验证
                verification_result = self._perform_expectation_verification(state)
                parsed_fields["expectation_verification_result"] = verification_result

                # 如果验证失败，不允许继续执行
                if not verification_result.get("verified", False):
                    logger.warning(f"[task_id: {task_id}] ❌ Expectation verification failed, blocking execution")
                    parsed_fields["execution_blocked"] = True
                    parsed_fields["block_reason"] = verification_result.get("reason", "Expectation verification failed")
                    # 验证失败时不更新last_step_name，保持原有状态以便下次重新验证
                    logger.info(f"[task_id: {task_id}] 🔄 Keeping last_step_name as '{self.last_step_name}' due to verification failure")
                else:
                    # 验证成功时才更新last_step_name
                    self.last_step_name = current_step_name
            elif is_rollback_after_verification_failure:
                logger.info(f"[task_id: {task_id}] ℹ️ Skipping verification due to rollback after verification failure")
                # 回滚后也更新last_step_name，因为这是重新执行的步骤
                self.last_step_name = current_step_name
            elif step_direction == "backward":
                logger.info(f"[task_id: {task_id}] ℹ️ Skipping verification due to backward step movement")
                # 向上回退时不验证，直接更新last_step_name
                self.last_step_name = current_step_name
            else:
                # 其他情况正常更新last_step_name
                self.last_step_name = current_step_name

            # 决策结束时间
            end_time = time.time()
            logger.info(
                f"[task_id: {task_id}] 决策耗时: {end_time - start_time:.2f}s")
            return parsed_fields, action_line
        except Exception as e:
            return AgentExceptionHandler.handle_decision_agent_exception(task_id, e, state)

    @staticmethod
    def _determine_step_direction(state: DeploymentState, current_step_name: str, last_step_name: str) -> str:
        """
        判断步骤切换方向

        Args:
            state: 当前状态
            current_step_name: 当前步骤名称
            last_step_name: 上一步骤名称

        Returns:
            "forward": 向下前进, "backward": 向上回退, "unknown": 无法判断
        """
        if not current_step_name or not last_step_name:
            return "unknown"

        task_steps = state.get("task_steps", [])
        if not task_steps:
            return "unknown"

        # 找到当前步骤和上一步骤在任务步骤列表中的索引
        current_index = -1
        last_index = -1

        for i, step_name in enumerate(task_steps):
            if step_name == current_step_name:
                current_index = i
            if step_name == last_step_name:
                last_index = i

        if current_index == -1 or last_index == -1:
            return "unknown"

        if current_index > last_index:
            return "forward"  # 向下前进
        elif current_index < last_index:
            return "backward"  # 向上回退
        else:
            return "unknown"  # 相同步骤

    @staticmethod
    def _parse_json_response(model_response: str, state: DeploymentState) -> \
            Tuple[Dict[str, Any], str]:
        """
            解析模型的JSON响应，提取各个字段

            Args:
                model_response: 模型的完整响应

            Returns:
                Tuple[parsed_fields, action_line]: 解析的字段字典、动作命令
            """
        action_line = ""
        parsed_fields = {
            "self_check": "",
            "interface_analysis": "",
            "current_step_name": "",
            "action_decision": "",
            "instruction": "",
            "element_description": "",
            "action": ""
        }
        try:
            # 尝试直接解析JSON
            json_data = json.loads(model_response.strip())
            parsed_fields.update(json_data)
            action_line = json_data.get("action", "")

        except json.JSONDecodeError as e:
            logger.error(f"[{state['task_id']}] ❌ JSON parsing error: {str(e)}")
            logger.error(f"[{state['task_id']}] Raw response: {model_response}")

            # 使用统一的异常处理方法
            TaskExceptionHandler.update_task_status_to_failed(
                state['task_id'],
                f"JSON parsing error in decision agent: {str(e)}"
            )

        return parsed_fields, action_line

    def _build_complete_test_case_messages(self, state: DeploymentState,
                                           image_data_base64: str) -> list:

        execution_records, image_records, has_execution_history = self.get_history_parameters(state)

        decision_prompt = get_decision_definition_prompt(state, execution_records, has_execution_history)

        invoke_prompt = get_execution_invoke_prompt()

        messages = [
            {
                "role": "system",
                "content": decision_prompt
            },
            {
                "role": "system",
                "content": invoke_prompt
            }
        ]

        if has_execution_history:
            # 构建历史截图内容列表
            content_list = []

            # 添加历史截图（最近5轮）
            for i, record in enumerate(image_records):
                before_screenshot_path = record.get("before_screenshot")
                if before_screenshot_path and before_screenshot_path != "":
                    try:
                        full_path = screenshot_manager.get_screenshot_full_path(before_screenshot_path)
                        with open(full_path, "rb") as f:
                            before_image_content = f.read()
                        before_image_data_base64 = base64.b64encode(before_image_content).decode("utf-8")

                        execution_count = record.get("execution_count", i + 1)
                        content_list.extend([
                            {
                                "type": "text",
                                "text": f"第{execution_count}轮界面截图"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{before_image_data_base64}",
                                    "detail": "high"
                                }
                            }
                        ])
                    except Exception as e:
                        logger.warning(f"Failed to load historical screenshot {before_screenshot_path}: {str(e)}")
                        continue

            # 如果有历史截图，添加包含所有图片的消息
            if content_list:
                messages.append(
                    {
                        "role": "user",
                        "content": "########## 执行记忆界面截图 ##########"
                    }
                )
                messages.append({
                    "role": "user",
                    "content": content_list
                })

                content_list.extend([
                    {
                        "type": "text",
                        "text": "########## 当前轮界面截图 ##########"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_data_base64}",
                            "detail": "high"
                        }
                    }
                ])
            else:
                # 如果没有历史截图，只添加当前截图
                messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "########## 当前轮界面截图 ##########"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_data_base64}",
                                "detail": "high"
                            }
                        }
                    ]
                })
        else:
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "########## 当前轮界面截图 ##########"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_data_base64}"
                        }
                    }
                ]
            })

        messages.append({
            "role": "user",
            "content": get_user_invoke_prompt()
        })
        return messages

    @staticmethod
    def get_history_parameters(state) -> tuple[list[Any], list[Any], bool]:
        history = state.get("history", [])
        execution_records = [r for r in history if
                             r.get("action") == "enhanced_get_location" and
                             (r.get("decision_content") or r.get(
                                 "decision_fields") or r.get("model_response"))]
        image_records = execution_records[-2:] if len(execution_records) > 2 else execution_records
        has_execution_history = len(execution_records) > 0
        print(len(execution_records), len(image_records))
        return execution_records, image_records, has_execution_history

    @staticmethod
    def _perform_expectation_verification(
            state: DeploymentState) -> Dict[str, Any]:
        """
        执行预期结果验证

        Args:
            state: 当前状态

        Returns:
            验证结果
        """
        try:
            task_id = state["task_id"]

            # 获取任务步骤列表
            task_steps = state.get("task_steps", [])
            
            # 从历史记录中获取最近执行的步骤名称
            history = state.get("history", [])
            execution_records = [r for r in history if r.get("action") == "enhanced_get_location"]
            
            if not execution_records:
                logger.info(f"[task_id: {task_id}] ℹ️ No execution history found, no verification needed")
                return {"verified": True, "reason": "No execution history"}
            
            # 获取最近一次执行的决策字段
            latest_record = execution_records[-1]
            decision_fields = latest_record.get("decision_fields", {})
            executed_step_name = decision_fields.get("current_step_name", "")
            
            if not executed_step_name:
                logger.warning(f"[task_id: {task_id}] ⚠️ No step name found in latest execution record")
                return {"verified": True, "reason": "No step name in execution record"}
            
            # 根据步骤名称找到对应的索引
            previous_step_index = -1
            for i, step_name in enumerate(task_steps):
                if step_name == executed_step_name:
                    previous_step_index = i
                    break
            
            if previous_step_index == -1:
                logger.warning(f"[task_id: {task_id}] ⚠️ Step name '{executed_step_name}' not found in task_steps")
                return {"verified": True, "reason": f"Step name '{executed_step_name}' not found"}

            logger.info(f"[task_id: {task_id}] 🔍 Found executed step: '{executed_step_name}' at index {previous_step_index}")

            # 获取当前步骤的期望结果
            step_expected_results = state.get("step_expected_results", [])
            expected_text = None
            expected_image = None

            if previous_step_index < len(step_expected_results):
                expected_result = step_expected_results[previous_step_index]
                expected_text = expected_result.get("text")
                expected_image = expected_result.get("image")

            logger.info(
                f"[task_id: {task_id}] 🔍 Performing expectation verification for completed step {previous_step_index + 1}: '{executed_step_name}'")

            # 调用预期结果验证agent，验证当前步骤
            verification_result = expectation_verification_agent.verify_step_expectation(
                state=state,
                previous_step_index=previous_step_index,
                expected_text=expected_text,
                expected_image=expected_image
            )

            return verification_result

        except Exception as e:
            logger.error(f"[{state['task_id']}] ❌ Error in expectation verification: {str(e)}")

            # 使用统一的异常处理方法
            TaskExceptionHandler.update_task_status_to_failed(
                state['task_id'],
                f"Expectation verification error: {str(e)}"
            )

            return {"verified": False, "reason": f"Verification error: {str(e)}"}
