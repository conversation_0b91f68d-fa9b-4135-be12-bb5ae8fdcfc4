"""
统一异常处理工具类
用于标准化整个系统中的异常处理流程
"""

import traceback
from datetime import datetime
from typing import Optional, Dict, Any

from src.infra.log import logger
from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
from src.schema.action_types import ExecutionStatus


class TaskExceptionHandler:
    """任务异常处理器"""

    @staticmethod
    def update_task_status_to_failed(task_id: str, error_message: str) -> None:
        """
        更新任务状态为失败的统一方法

        Args:
            task_id: 任务ID
            error_message: 错误信息
        """
        try:
            task_persistence_service.update_task_status(
                task_id=task_id,
                status=ExecutionStatus.FAILED,
                error_message=error_message
            )
            logger.info(f"[{task_id}] 📝 Task status updated to FAILED: {error_message}")
        except Exception as db_error:
            logger.error(f"[{task_id}] ❌ Failed to update task status in database: {str(db_error)}")

    @staticmethod
    def handle_task_exception(
        task_id: str,
        error: Exception,
        context: str = "",
        update_task_status: bool = True,
        state: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        统一处理任务异常

        Args:
            task_id: 任务ID
            error: 异常对象
            context: 异常上下文描述
            update_task_status: 是否更新任务状态为失败
            state: 任务状态字典（可选）
        """
        error_message = str(error)
        full_error_message = f"{context}: {error_message}" if context else error_message

        # 记录详细的错误信息
        logger.error(f"[{task_id}] ❌ {context} failed: {error_message}")
        logger.error(f"[{task_id}] Exception traceback: {traceback.format_exc()}")

        # 更新状态字典（如果提供）
        if state is not None:
            state["completed"] = True
            state["execution_status"] = "failed"
            state["error_message"] = full_error_message

            # 添加错误记录到历史中
            state.setdefault("history", []).append({
                "action": "exception_handling",
                "context": context,
                "error": error_message,
                "status": "error",
                "timestamp": datetime.now().isoformat()
            })

        # 更新数据库中的任务状态
        if update_task_status:
            TaskExceptionHandler.update_task_status_to_failed(task_id, full_error_message)
    
    @staticmethod
    def complete_action_as_failed(
        task_id: str,
        action_id: int,
        error_message: str,
        action_command: str = ""
    ) -> None:
        """
        将动作标记为失败的统一方法

        Args:
            task_id: 任务ID
            action_id: 动作ID
            error_message: 错误信息
            action_command: 动作命令
        """
        try:
            task_persistence_service.complete_task_action(
                action_id=action_id,
                success=False,
                error_message=error_message,
                after_screenshot=None,
                verification_result=None,
                final_action_command=action_command
            )
            logger.info(f"[{task_id}] 📝 Action {action_id} marked as failed: {error_message}")
        except Exception as db_error:
            logger.error(f"[{task_id}] ❌ Failed to complete action {action_id}: {str(db_error)}")

    @staticmethod
    def handle_action_exception(
        task_id: str,
        action_id: Optional[int],
        error: Exception,
        context: str = "",
        action_command: str = ""
    ) -> None:
        """
        统一处理动作异常

        Args:
            task_id: 任务ID
            action_id: 动作ID（可选）
            error: 异常对象
            context: 异常上下文描述
            action_command: 动作命令
        """
        error_message = str(error)
        full_error_message = f"{context}: {error_message}" if context else error_message

        # 记录详细的错误信息
        logger.error(f"[{task_id}] ❌ {context} failed: {error_message}")
        logger.error(f"[{task_id}] Action exception traceback: {traceback.format_exc()}")

        # 完成动作记录（如果提供了action_id）
        if action_id is not None:
            TaskExceptionHandler.complete_action_as_failed(
                task_id, action_id, full_error_message, action_command
            )
    
    @staticmethod
    def should_fail_task_on_repeated_errors(
        state: Dict[str, Any],
        error_type: str,
        max_errors: int = 3
    ) -> bool:
        """
        检查是否应该因为重复错误而使任务失败
        
        Args:
            state: 任务状态字典
            error_type: 错误类型
            max_errors: 最大错误次数
            
        Returns:
            是否应该使任务失败
        """
        history = state.get("history", [])
        recent_errors = [
            h for h in history[-10:]  # 检查最近10条记录
            if h.get("status") == "error" and error_type in h.get("error", "")
        ]
        
        return len(recent_errors) >= max_errors
    
    @staticmethod
    def log_execution_failure(task_id: str, error_message: str) -> None:
        """
        记录执行失败日志

        Args:
            task_id: 任务ID
            error_message: 错误信息
        """
        try:
            failure_log = f"\n[{datetime.now().strftime('%m-%d %H:%M:%S')}] 任务执行失败 - 错误: {error_message}"
            task_persistence_service.append_execution_log(task_id, failure_log)
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to log execution failure: {str(e)}")


class AgentExceptionHandler:
    """Agent异常处理器"""
    
    @staticmethod
    def handle_decision_agent_exception(
        task_id: str,
        error: Exception,
        state: Optional[Dict[str, Any]] = None
    ) -> tuple:
        """
        处理决策Agent异常
        
        Args:
            task_id: 任务ID
            error: 异常对象
            state: 任务状态字典（可选）
            
        Returns:
            空的解析字段和动作命令
        """
        TaskExceptionHandler.handle_task_exception(
            task_id=task_id,
            error=error,
            context="Decision agent",
            state=state
        )
        return {}, ""
    
    @staticmethod
    def handle_execution_agent_exception(
        task_id: str,
        error: Exception,
        max_retries: int,
        current_attempt: int
    ) -> dict:
        """
        处理执行Agent异常
        
        Args:
            task_id: 任务ID
            error: 异常对象
            max_retries: 最大重试次数
            current_attempt: 当前尝试次数
            
        Returns:
            错误结果字典
        """
        if current_attempt >= max_retries:
            TaskExceptionHandler.handle_task_exception(
                task_id=task_id,
                error=error,
                context="Execution agent coordinate extraction"
            )
        
        return {
            "action": "",
            "full_response": "",
            "thought": "",
            "validation_result": {"is_valid": False, "errors": [f"异常: {str(error)}"]},
            "attempt": current_attempt + 1,
            "error": str(error)
        }
    
    @staticmethod
    def handle_supervisor_agent_exception(
        task_id: str,
        error: Exception,
        context: str = "Supervisor agent"
    ) -> bool:
        """
        处理监督Agent异常
        
        Args:
            task_id: 任务ID
            error: 异常对象
            context: 异常上下文
            
        Returns:
            保守的处理结果（通常返回True表示有问题）
        """
        TaskExceptionHandler.handle_task_exception(
            task_id=task_id,
            error=error,
            context=context
        )
        return True  # 保守地认为有问题
