#!/usr/bin/env python3
"""
ADB连接管理器

负责管理Android设备的ADB连接，包括远程设备的连接和断开
使用原生ADB命令，提供更稳定的连接管理
"""

import time
from typing import Set, Optional

from loguru import logger

from .native_adb_utils import native_adb


class ADBConnectionManager:
    """ADB连接管理器"""

    def __init__(self):
        """初始化连接管理器"""
        self._connected_devices: Set[str] = set()

    def is_remote_device(self, device_id: str) -> bool:
        """
        判断是否为远程设备

        Args:
            device_id: 设备ID

        Returns:
            是否为远程设备
        """
        return native_adb.is_remote_device(device_id)
    
    def connect_device(self, device_id: str, task_id: str = None) -> bool:
        """
        连接设备

        Args:
            device_id: 设备ID
            task_id: 任务ID（用于日志）

        Returns:
            是否连接成功
        """
        log_prefix = f"[{task_id}] " if task_id else ""
        try:
            # 如果是远程设备，需要先连接
            if self.is_remote_device(device_id):
                logger.info(f"{log_prefix}🔗 Connecting to remote device: {device_id}")

                # 使用原生adb连接远程设备
                if not native_adb.connect_device(device_id):
                    logger.error(f"{log_prefix}❌ Failed to connect to remote device {device_id}")
                    return False

                logger.info(f"{log_prefix}✅ Successfully connected to remote device: {device_id}")

                # 等待连接稳定
                time.sleep(1)

            # 验证设备是否可用
            if self.is_device_available(device_id, task_id):
                self._connected_devices.add(device_id)
                logger.info(f"{log_prefix}✅ Device {device_id} is ready for use")
                return True
            else:
                logger.error(f"{log_prefix}❌ Device {device_id} is not available after connection")
                return False
                
        except Exception as e:
            logger.error(f"{log_prefix}❌ Error connecting device {device_id}: {str(e)}")
            return False
    
    def disconnect_device(self, device_id: str, task_id: str = None) -> bool:
        """
        断开设备连接
        
        Args:
            device_id: 设备ID
            task_id: 任务ID（用于日志）
            
        Returns:
            是否断开成功
        """
        log_prefix = f"[{task_id}] " if task_id else ""
        try:

            # 如果是远程设备，需要断开连接
            if self.is_remote_device(device_id) and device_id in self._connected_devices:
                logger.info(f"{log_prefix}🔌 Disconnecting remote device: {device_id}")

                if not native_adb.disconnect_device(device_id):
                    logger.warning(f"{log_prefix}⚠️ Error disconnecting remote device {device_id}")
                    # 即使断开失败，也要清理本地状态
                else:
                    logger.info(f"{log_prefix}✅ Successfully disconnected remote device: {device_id}")

                # 清理本地状态
                self._connected_devices.discard(device_id)

                return True
            else:
                # 本地设备或未连接的设备，只清理状态
                self._connected_devices.discard(device_id)
                logger.info(f"{log_prefix}✅ Device {device_id} state cleaned")
                return True
                
        except Exception as e:
            logger.error(f"{log_prefix}❌ Error disconnecting device {device_id}: {str(e)}")
            return False
    
    def get_device(self, device_id: str) -> Optional[str]:
        """
        获取设备ID（兼容性方法）

        Args:
            device_id: 设备ID

        Returns:
            设备ID，失败返回None
        """
        try:
            # 检查设备是否可用
            if native_adb.is_device_available(device_id):
                return device_id
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error getting device {device_id}: {str(e)}")
            return None
    
    def is_device_available(self, device_id: str, task_id: str = None) -> bool:
        """
        检查设备是否可用

        Args:
            device_id: 设备ID
            task_id: 任务ID（用于日志）

        Returns:
            设备是否可用
        """
        log_prefix = f"[{task_id}] " if task_id else ""
        try:
            # 使用原生adb检查设备状态
            if native_adb.is_device_available(device_id):
                logger.debug(f"{log_prefix}✅ Device {device_id} is responsive")
                return True
            else:
                logger.error(f"{log_prefix}❌ Device {device_id} is not responsive")
                return False

        except Exception as e:
            logger.error(f"{log_prefix}❌ Error checking device availability {device_id}: {str(e)}")
            return False
    
    def list_connected_devices(self) -> list:
        """
        列出所有已连接的设备

        Returns:
            设备ID列表
        """
        try:
            device_ids = native_adb.list_devices()
            logger.info(f"📱 Connected devices: {device_ids}")
            return device_ids
        except Exception as e:
            logger.error(f"❌ Error listing devices: {str(e)}")
            return []
    
    def cleanup_all_connections(self):
        """清理所有连接"""
        logger.info("🧹 Cleaning up all ADB connections...")

        # 断开所有已连接的远程设备
        for device_id in list(self._connected_devices):
            if self.is_remote_device(device_id):
                self.disconnect_device(device_id)

        # 清理缓存
        self._connected_devices.clear()

        logger.info("✅ All ADB connections cleaned up")


# 全局连接管理器实例
adb_connection_manager = ADBConnectionManager()


def connect_device(device_id: str, task_id: str = None) -> bool:
    """
    连接设备的便捷函数
    
    Args:
        device_id: 设备ID
        task_id: 任务ID
        
    Returns:
        是否连接成功
    """
    return adb_connection_manager.connect_device(device_id, task_id)


def disconnect_device(device_id: str, task_id: str = None) -> bool:
    """
    断开设备连接的便捷函数
    
    Args:
        device_id: 设备ID
        task_id: 任务ID
        
    Returns:
        是否断开成功
    """
    return adb_connection_manager.disconnect_device(device_id, task_id)


def get_device(device_id: str) -> Optional[str]:
    """
    获取设备ID的便捷函数（兼容性）

    Args:
        device_id: 设备ID

    Returns:
        设备ID
    """
    return adb_connection_manager.get_device(device_id)


def is_device_available(device_id: str, task_id: str = None) -> bool:
    """
    检查设备是否可用的便捷函数
    
    Args:
        device_id: 设备ID
        task_id: 任务ID
        
    Returns:
        设备是否可用
    """
    return adb_connection_manager.is_device_available(device_id, task_id)
