#!/usr/bin/env python3
"""
截图清理定时任务
"""

from loguru import logger
from src.domain.ui_task.mobile.android.screenshot_manager import screenshot_manager
from src.infra.scheduler import app_scheduler


def cleanup_old_screenshots():
    """
    清理5天前的截图文件
    每天凌晨3点执行
    """
    try:
        logger.info("🧹 Starting scheduled screenshot cleanup task...")
        
        # 清理5天前的截图
        deleted_count = screenshot_manager.cleanup_old_screenshots(days_to_keep=5)
        
        if deleted_count > 0:
            logger.info(f"✅ Screenshot cleanup completed successfully, deleted {deleted_count} old screenshots")
        else:
            logger.info("✅ Screenshot cleanup completed, no old screenshots to delete")
            
    except Exception as e:
        logger.error(f"❌ Screenshot cleanup task failed: {str(e)}")
        import traceback
        traceback.print_exc()


# 添加定时任务：每天凌晨3点执行截图清理
app_scheduler.add_job(
    cleanup_old_screenshots,
    "cron",
    hour=3,
    minute=0,
    id=app_scheduler.job_id("cleanup_old_screenshots"),
    replace_existing=True
)

logger.info("📅 Screenshot cleanup scheduled task registered: daily at 3:00 AM")
